# Stripe Integration Guide

This guide explains how to set up and use the Stripe integration in Alden.

## Overview

The Stripe integration allows organizations to connect their Stripe accounts using OAuth 2.0 flow. Once connected, the integration stores access tokens and account information securely in the database for future API calls.

## Features

- **OAuth 2.0 Flow**: Secure connection using Stripe Connect
- **Environment Support**: Separate connections for development (test) and production (live) modes
- **Token Storage**: Access tokens stored in database settings (plain text as requested)
- **Organization-based**: Each organization can have separate Stripe integrations
- **Status Tracking**: Track connection status and metadata

## Setup Instructions

### 1. Stripe Account Setup

1. **Create a Stripe Connect Application**:
   - Go to [Stripe Dashboard](https://dashboard.stripe.com)
   - Navigate to Settings → Connect
   - Create a new Connect application
   - Note down your `Client ID` and `Secret Key`

2. **Configure OAuth Settings**:
   - Set the redirect URI to: `https://yourdomain.com/api/integrations/stripe/callback`
   - For local development: `http://localhost:3000/api/integrations/stripe/callback`

### 2. Environment Variables

Add the following environment variables to your `.env.local` file:

```bash
# Stripe Integration
STRIPE_CLIENT_ID=ca_...                    # Your Stripe Connect Client ID
STRIPE_SECRET_KEY=sk_test_...              # Your Stripe Secret Key (test or live)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_... # Your Stripe Publishable Key

# Application URLs
NEXT_PUBLIC_BASE_URL=http://localhost:3000  # Your application base URL
```

### 3. Database Schema

The integration uses the existing `org_integrations` table with the following structure:

```sql
CREATE TABLE "org_integrations" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "org_id" varchar(128) NOT NULL,
  "provider" integration_provider NOT NULL,
  "environment" environment DEFAULT 'production' NOT NULL,
  "external_id" varchar(255),           -- Stripe Account ID (acct_xxx)
  "settings" jsonb DEFAULT '{}'::jsonb NOT NULL,
  "secret_ref" text,                    -- Empty as requested
  "status" integration_status DEFAULT 'pending' NOT NULL,
  "connected_at" timestamp,
  "revoked_at" timestamp,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);
```

## Usage

### 1. Frontend Integration

Users can connect their Stripe account by visiting the `/integrate` page:

1. Select environment (Production or Development)
2. Click "Connect with Stripe"
3. Complete OAuth flow on Stripe's website
4. Get redirected back with success/error status

### 2. API Endpoints

#### Initiate OAuth Flow

```
GET /api/integrations/stripe/connect?environment=production
```

#### OAuth Callback (handled automatically)

```
GET /api/integrations/stripe/callback?code=...&state=...
```

#### Check Integration Status

```
GET /api/integrations/stripe/status?environment=production
```

### 3. Database Integration

Use the provided utility functions in `src/lib/integrations.ts`:

```typescript
import {
  getStripeIntegration,
  upsertStripeIntegration,
  getAccessTokenFromIntegration,
} from '../lib/integrations';

// Get integration for an organization
const integration = await getStripeIntegration(orgId, 'production');

// Extract access token for API calls
const accessToken = getAccessTokenFromIntegration(integration);
```

## Data Storage

### Settings Structure

The integration settings are stored in the `settings` JSONB column:

```json
{
  "accessToken": "sk_test_...",
  "refreshToken": "rt_...",
  "livemode": false,
  "scope": "read_write",
  "stripeUserId": "acct_...",
  "stripePublishableKey": "pk_test_...",
  "secrets": {
    "accessToken": "sk_test_...",
    "refreshToken": "rt_..."
  }
}
```

### Security Notes

- Access tokens are stored in plain text as requested
- `secretRef` field is kept empty as requested
- Consider implementing token encryption in production
- Tokens are stored in both root level and `secrets` object for flexibility

## Error Handling

The integration handles various error scenarios:

- **OAuth Errors**: Redirected back to integrate page with error parameters
- **Invalid State**: CSRF protection with state parameter validation
- **Expired State**: State parameters expire after 1 hour
- **Missing Configuration**: Proper error messages for missing environment variables
- **Database Errors**: Graceful handling of database connection issues

## Testing

### Development Mode

1. Use Stripe test keys (`sk_test_...`, `pk_test_...`)
2. Set environment to "development" in the UI
3. Use test Stripe accounts for OAuth flow

### Production Mode

1. Use Stripe live keys (`sk_live_...`, `pk_live_...`)
2. Set environment to "production" in the UI
3. Use real Stripe accounts for OAuth flow

## Troubleshooting

### Common Issues

1. **OAuth Redirect Mismatch**:
   - Ensure redirect URI in Stripe matches your callback URL exactly
   - Check `NEXT_PUBLIC_BASE_URL` environment variable

2. **Missing Environment Variables**:
   - Verify all required Stripe environment variables are set
   - Check both client ID and secret key are correct

3. **Database Connection**:
   - Ensure `DATABASE_URL` is properly configured
   - Check database migrations are up to date

4. **Organization Context**:
   - User must be part of an organization to connect integrations
   - Check Clerk organization setup

### Debug Mode

Enable debug logging by checking browser console and server logs for detailed error messages during the OAuth flow.

how much individual?
AI IS INPUT
