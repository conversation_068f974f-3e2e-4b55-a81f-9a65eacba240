const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
require('dotenv/config');

const sql = postgres(process.env.DATABASE_URL);
const db = drizzle(sql);

async function checkIntegrations() {
  try {
    const result = await sql`
      SELECT id, org_id, provider, environment, external_id, status, connected_at, created_at
      FROM org_integrations 
      ORDER BY created_at DESC 
      LIMIT 10
    `;
    
    console.log('Current integrations:');
    console.table(result);
    
    // Check for duplicates
    const duplicates = await sql`
      SELECT org_id, provider, environment, COUNT(*) as count
      FROM org_integrations 
      GROUP BY org_id, provider, environment
      HAVING COUNT(*) > 1
    `;
    
    console.log('\nDuplicate check (should be empty due to unique constraint):');
    console.table(duplicates);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sql.end();
  }
}

checkIntegrations();
