This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Features

### Authentication & Onboarding Flow

The application implements a comprehensive authentication and onboarding flow:

1. **User Authentication**: Uses Clerk for user authentication
2. **Organization Setup**: Users must create or join an organization
3. **Integration Setup**: Users must connect at least one integration (currently Stripe)
4. **Automatic Redirects**:
   - Users without organizations are redirected to `/setup`
   - Users without integrations are redirected to `/integrate`
   - Users with both can access the main dashboard at `/home`

### Stripe Integration

The application includes a complete Stripe Connect integration:

- **OAuth Flow**: Secure Stripe Connect OAuth implementation
- **Test Environment Safety**: All integrations are validated to ensure they're in test mode
- **Token Management**: Secure storage of Stripe access tokens in the database

### Test Data Generation

A powerful test data generation system for Stripe integrations:

#### Features:

- **Safety First**: Multiple safety checks prevent accidental data creation in live environments
- **Realistic Data**: Generates realistic SaaS business data with growth patterns
- **Product Variety**: Creates 5 standalone products and 5 subscription products
- **Growth Simulation**: Simulates 2-5% monthly revenue growth over the specified period
- **50/50 Split**: Revenue is split evenly between standalone and subscription products

#### Usage:

**API Endpoint**: `POST /api/integrations/stripe/ingest-test-data`

**Request Body**:

```json
{
  "orgIntegrationId": "uuid-of-integration",
  "numberOfInvoices": 100,
  "numberOfCustomers": 20,
  "periodMonths": 12
}
```

**Example Products Created**:

_Standalone Products_:

- Premium Analytics Report ($49.00)
- Custom Dashboard Setup ($129.00)
- Data Migration Service ($299.00)
- Advanced Integration Package ($199.00)
- Priority Support Session ($99.00)

_Subscription Products_:

- Starter Plan ($29.00/month)
- Professional Plan ($79.00/month)
- Enterprise Plan ($199.00/month)
- Team Plan ($149.00/month)
- Premium Plan ($399.00/month)

#### Safety Measures:

- ✅ Validates integration exists and belongs to the user's organization
- ✅ Ensures integration is in test mode (`livemode: false`)
- ✅ Ensures integration environment is `development`
- ✅ Multiple validation layers prevent accidental live data creation

#### Implementation:

The test data generation is implemented as an Inngest function (`ingestTestData`) that:

1. Validates the integration and safety requirements
2. Creates realistic SaaS products and pricing
3. Generates test customers
4. Creates backdated invoices with growth patterns
5. Simulates successful payments using Stripe test tokens

This allows developers and users to quickly populate their test Stripe accounts with realistic data for testing dashboards, analytics, and other features.
