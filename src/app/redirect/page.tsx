'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect } from 'react';
import toast from 'react-hot-toast';

function CallbackComponent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const message = searchParams.get('message');
    const redirect = searchParams.get('redirect');

    if (message) {
      toast.success(decodeURIComponent(message));
    }

    const redirectPath = redirect ? decodeURIComponent(redirect) : '/home';
    router.push(redirectPath);
  }, [searchParams, router]);

  return <div>Redirecting...</div>;
}

export default function CallbackSuccessPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CallbackComponent />
    </Suspense>
  );
}
