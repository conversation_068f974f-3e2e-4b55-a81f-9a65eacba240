import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { ReactNode } from 'react';
import { Sidebar, TopHeader } from '../../components';
import { hasActiveOrgIntegration } from '../../lib/integrations';

interface DashboardLayoutProps {
  children: ReactNode;
}

export default async function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  const { userId, orgId } = await auth();
  if (!userId) return redirect('/sign-in');
  if (!orgId) return redirect('/setup');

  const hasActiveIntegration = await hasActiveOrgIntegration(orgId);
  if (!hasActiveIntegration) return redirect('/integrate');

  return (
    <div className='min-h-screen bg-gray-50 flex'>
      {/* Sidebar */}
      <Sidebar />

      {/* Main content area */}
      <div className='flex-1 flex flex-col'>
        {/* Top header */}
        <TopHeader />

        {/* Page content */}
        <main className='flex-1 p-6'>{children}</main>
      </div>
    </div>
  );
}
