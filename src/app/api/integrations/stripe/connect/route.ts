import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Authenticate the user
    const { userId, orgId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!orgId) {
      return NextResponse.json(
        { error: 'Organization required' },
        { status: 400 }
      );
    }

    const baseUrl = process.env.VERCEL_URL
      ? `https://${process.env.VERCEL_URL}`
      : 'http://localhost:3000'; // Default for local development

    // Determine environment based on Vercel environment variables
    const environment =
      process.env.NODE_ENV === 'production' ? 'production' : 'development';

    // Validate required environment variables
    const clientId = process.env.STRIPE_CLIENT_ID;

    if (!clientId) {
      console.error('STRIPE_CLIENT_ID environment variable is not set');
      return NextResponse.json(
        { error: 'Stripe configuration missing' },
        { status: 500 }
      );
    }

    // Construct the callback URL
    const callbackUrl = `${baseUrl}/api/integrations/stripe/callback`;

    // Create state parameter to prevent CSRF attacks and pass org info
    const state = Buffer.from(
      JSON.stringify({
        orgId,
        userId,
        environment,
        timestamp: Date.now(),
      })
    ).toString('base64url');

    // Construct Stripe OAuth URL
    const stripeOAuthUrl = new URL(
      'https://connect.stripe.com/oauth/authorize'
    );
    stripeOAuthUrl.searchParams.set('response_type', 'code');
    stripeOAuthUrl.searchParams.set('client_id', clientId);
    stripeOAuthUrl.searchParams.set('scope', 'read_write');
    stripeOAuthUrl.searchParams.set('redirect_uri', callbackUrl);
    stripeOAuthUrl.searchParams.set('state', state);

    // Add suggested capabilities for better UX
    stripeOAuthUrl.searchParams.set(
      'suggested_capabilities[]',
      'card_payments'
    );
    stripeOAuthUrl.searchParams.set('suggested_capabilities[]', 'transfers');

    return NextResponse.json({
      url: stripeOAuthUrl.toString(),
      state,
    });
  } catch (error) {
    console.error('Error creating Stripe OAuth URL:', error);
    return NextResponse.json(
      { error: 'Failed to create OAuth URL' },
      { status: 500 }
    );
  }
}
