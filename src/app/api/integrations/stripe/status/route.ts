import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { getStripeIntegration } from '../../../../../lib/integrations';

export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const { userId, orgId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!orgId) {
      return NextResponse.json(
        { error: 'Organization required' },
        { status: 400 }
      );
    }

    // Determine environment based on Vercel environment variables
    const vercelEnv =
      process.env.VERCEL_ENV || process.env.NODE_ENV || 'production';

    // Map Vercel environments to our environment types
    let environment: 'development' | 'production';
    if (vercelEnv === 'development' || vercelEnv === 'preview') {
      environment = 'development';
    } else if (vercelEnv === 'production') {
      environment = 'production';
    } else {
      return NextResponse.json(
        { error: 'Invalid environment' },
        { status: 400 }
      );
    }

    // Get the integration
    const integration = await getStripeIntegration(
      orgId,
      environment as 'development' | 'production'
    );

    if (!integration) {
      return NextResponse.json({
        connected: false,
        environment,
      });
    }

    // Return integration status (without sensitive data)
    return NextResponse.json({
      connected: true,
      environment,
      status: integration.status,
      externalId: integration.externalId,
      connectedAt: integration.connectedAt,
      livemode: integration.settings?.livemode || false,
    });
  } catch (error) {
    console.error('Error checking Stripe integration status:', error);
    return NextResponse.json(
      { error: 'Failed to check integration status' },
      { status: 500 }
    );
  }
}
