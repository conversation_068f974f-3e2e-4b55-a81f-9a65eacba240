import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { inngest } from '../../../../../ingest/client';
import { getIntegrationById } from '../../../../../lib/integrations';

export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const { userId, orgId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!orgId) {
      return NextResponse.json(
        { error: 'Organization required' },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { 
      orgIntegrationId, 
      numberOfInvoices = 100, 
      numberOfCustomers = 20, 
      periodMonths = 12 
    } = body;

    if (!orgIntegrationId) {
      return NextResponse.json(
        { error: 'orgIntegrationId is required' },
        { status: 400 }
      );
    }

    // Validate that the integration exists and belongs to the user's org
    const integration = await getIntegrationById(orgIntegrationId);
    if (!integration) {
      return NextResponse.json(
        { error: 'Integration not found' },
        { status: 404 }
      );
    }

    if (integration.orgId !== orgId) {
      return NextResponse.json(
        { error: 'Integration does not belong to your organization' },
        { status: 403 }
      );
    }

    // Safety check: Ensure this is NOT a live integration
    if (integration.settings?.livemode === true) {
      return NextResponse.json(
        { error: 'SAFETY ERROR: Cannot populate test data on a live Stripe integration!' },
        { status: 400 }
      );
    }

    if (integration.environment === 'production') {
      return NextResponse.json(
        { error: 'SAFETY ERROR: Cannot populate test data on a production environment integration!' },
        { status: 400 }
      );
    }

    // Trigger the Inngest function
    const event = await inngest.send({
      name: 'stripe/ingest.test.data',
      data: {
        orgIntegrationId,
        numberOfInvoices,
        numberOfCustomers,
        periodMonths,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Test data ingestion job started',
      eventId: event.ids[0],
      parameters: {
        orgIntegrationId,
        numberOfInvoices,
        numberOfCustomers,
        periodMonths,
      },
    });
  } catch (error) {
    console.error('Error starting test data ingestion:', error);
    return NextResponse.json(
      { error: 'Failed to start test data ingestion' },
      { status: 500 }
    );
  }
}
