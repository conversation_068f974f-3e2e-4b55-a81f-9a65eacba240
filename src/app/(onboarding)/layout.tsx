// this is a layout that has alend logo on top, and action in the center
import { OrganizationSwitcher, UserButton } from '@clerk/nextjs';
import { auth } from '@clerk/nextjs/server';
import { ReactNode } from 'react';

interface OnboardingLayoutProps {
  children: ReactNode;
}

export default async function OnboardingLayout({
  children,
}: OnboardingLayoutProps) {
  await auth.protect();

  // todo in stripe setup, and paymment, you should be able to select your org
  return (
    <>
      <header className='bg-white border-b border-gray-200 px-6 py-4'>
        <div className='flex items-center justify-between'>
          {/* Page title */}
          Alden logo here / Mandatory step
          {/* Right side - Ask AI button and user profile */}
          <div className='flex items-center space-x-4'>
            {/* User profile */}
            <UserButton />
            {/* Org switcher */}
            <OrganizationSwitcher skipInvitationScreen hidePersonal />
          </div>
        </div>
      </header>

      <main className='flex-1 p-6'>{children}</main>
    </>
  );
}
