// this is a page to create a new org, it does not contain dashboard layout
import { CreateOrganization } from '@clerk/nextjs';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

export default async function SetupPage() {
  const { orgId } = await auth();

  if (orgId) redirect('/home');

  return (
    <div className='flex justify-center py-24 flex flex-col items-center'>
      <h1 className='text-2xl font-semibold text-gray-900 mb-4'>
        Create your organization
      </h1>
      <CreateOrganization />
    </div>
  );
}
