import Stripe from 'stripe';
import {
  getAccessTokenFromIntegration,
  getIntegrationById,
} from '../lib/integrations';
import { inngest } from './client';

export const helloWorld = inngest.createFunction(
  { id: 'hello-world' },
  { event: 'test/hello.world' },
  async ({ event, step }) => {
    await step.sleep('wait-a-moment', '1s');
    return { message: `Hello ${event.data.email}!` };
  }
);

interface IngestTestDataParams {
  orgIntegrationId: string;
  numberOfInvoices: number;
  numberOfCustomers: number;
  periodMonths: number;
}

// SaaS product templates
const STANDALONE_PRODUCTS = [
  { name: 'Premium Analytics Report', price: 4900 }, // $49.00
  { name: 'Custom Dashboard Setup', price: 12900 }, // $129.00
  { name: 'Data Migration Service', price: 29900 }, // $299.00
  { name: 'Advanced Integration Package', price: 19900 }, // $199.00
  { name: 'Priority Support Session', price: 9900 }, // $99.00
];

const SUBSCRIPTION_PRODUCTS = [
  { name: 'Starter Plan', price: 2900, interval: 'month' }, // $29.00/month
  { name: 'Professional Plan', price: 7900, interval: 'month' }, // $79.00/month
  { name: 'Enterprise Plan', price: 19900, interval: 'month' }, // $199.00/month
  { name: 'Team Plan', price: 14900, interval: 'month' }, // $149.00/month
  { name: 'Premium Plan', price: 39900, interval: 'month' }, // $399.00/month
];

export const ingestTestData = inngest.createFunction(
  { id: 'ingest-test-data' },
  { event: 'stripe/ingest.test.data' },
  async ({ event }) => {
    const {
      orgIntegrationId,
      numberOfInvoices,
      numberOfCustomers,
      periodMonths,
    } = event.data as IngestTestDataParams;

    // Get and validate integration
    const integration = await getIntegrationById(orgIntegrationId);
    if (!integration) {
      throw new Error(`Integration with ID ${orgIntegrationId} not found`);
    }

    // SAFETY CHECK: Ensure this is NOT a live integration
    if (integration.settings?.livemode === true) {
      throw new Error(
        'SAFETY ERROR: Cannot populate test data on a live Stripe integration!'
      );
    }

    if (integration.environment === 'production') {
      throw new Error(
        'SAFETY ERROR: Cannot populate test data on a production environment integration!'
      );
    }

    // Initialize Stripe client
    const accessToken = getAccessTokenFromIntegration(integration);
    if (!accessToken) {
      throw new Error('No access token found for integration');
    }

    const stripe = new Stripe(accessToken, {
      apiVersion: '2025-08-27.basil',
    });

    console.log('WORKING');

    // Create products
    const createdProducts = [];

    // Create standalone products
    for (const product of STANDALONE_PRODUCTS) {
      const stripeProduct = await stripe.products.create({
        name: product.name,
        type: 'service',
        metadata: { type: 'standalone' },
      });

      console.log('Creatig product', stripeProduct);

      const price = await stripe.prices.create({
        product: stripeProduct.id,
        unit_amount: product.price,
        currency: 'usd',
      });

      createdProducts.push({
        product: stripeProduct,
        price,
        type: 'standalone',
        amount: product.price,
      });
    }

    // Create subscription products
    for (const product of SUBSCRIPTION_PRODUCTS) {
      const stripeProduct = await stripe.products.create({
        name: product.name,
        type: 'service',
        metadata: { type: 'subscription' },
      });

      console.log('Creating subscription product', stripeProduct);

      const price = await stripe.prices.create({
        product: stripeProduct.id,
        unit_amount: product.price,
        currency: 'usd',
        recurring: { interval: product.interval as 'month' },
      });

      createdProducts.push({
        product: stripeProduct,
        price,
        type: 'subscription',
        amount: product.price,
      });
    }

    // Create customers
    const createdCustomers = [];
    for (let i = 0; i < numberOfCustomers; i++) {
      const customer = await stripe.customers.create({
        email: `test-customer-${i + 1}@example.com`,
        name: `Test Customer ${i + 1}`,
        description: `Generated test customer ${i + 1}`,
      });
      createdCustomers.push(customer);
    }

    console.log('Created customers', createdCustomers);

    // Generate invoices with growth pattern and churn
    const createdInvoices = [];
    const yearlyChurnRate = 0.05; // 5% yearly churn
    const monthlyChurnRate = yearlyChurnRate / 12; // Convert to monthly churn rate

    // Track active subscriptions for churn simulation
    const activeSubscriptions = new Map<
      string,
      { customerId: string; productId: string; startMonth: number }
    >();
    let subscriptionCounter = 0;

    for (let month = 0; month < periodMonths; month++) {
      const invoicesThisMonth = Math.floor(numberOfInvoices / periodMonths);

      // Calculate date for this month (going backwards from now)
      const invoiceDate = new Date();
      invoiceDate.setMonth(invoiceDate.getMonth() - (periodMonths - month - 1));

      // Handle subscription churn - cancel some existing subscriptions
      if (month > 0) {
        const subscriptionsToCancel = [];
        for (const [subId, sub] of activeSubscriptions.entries()) {
          // Only consider subscriptions that have been active for at least 1 month
          if (month - sub.startMonth >= 1 && Math.random() < monthlyChurnRate) {
            subscriptionsToCancel.push(subId);
          }
        }

        // Remove churned subscriptions
        subscriptionsToCancel.forEach(subId => {
          activeSubscriptions.delete(subId);
        });
      }

      for (let i = 0; i < invoicesThisMonth; i++) {
        // Randomly choose between standalone and subscription (50/50 split)
        const isSubscription = Math.random() < 0.5;
        const productPool = isSubscription
          ? createdProducts.filter(p => p.type === 'subscription')
          : createdProducts.filter(p => p.type === 'standalone');

        const selectedProduct =
          productPool[Math.floor(Math.random() * productPool.length)];
        const customer =
          createdCustomers[Math.floor(Math.random() * createdCustomers.length)];

        // For subscriptions, track them for churn simulation
        if (isSubscription) {
          const subId = `sub_${subscriptionCounter++}`;
          activeSubscriptions.set(subId, {
            customerId: customer.id,
            productId: selectedProduct.product.id,
            startMonth: month,
          });
        }

        // Create invoice item first
        await stripe.invoiceItems.create({
          customer: customer.id,
          amount: selectedProduct.amount,
          currency: 'usd',
          description: selectedProduct.product.name,
          metadata: {
            generated: 'true',
            month: month.toString(),
            type: selectedProduct.type,
          },
        });

        // Create and finalize invoice
        const invoice = await stripe.invoices.create({
          customer: customer.id,
          collection_method: 'charge_automatically',
          auto_advance: false, // We'll finalize manually
          metadata: {
            generated: 'true',
            month: month.toString(),
            type: selectedProduct.type,
          },
        });

        // Finalize the invoice
        if (invoice.id) {
          const finalizedInvoice = await stripe.invoices.finalizeInvoice(
            invoice.id
          );

          // Mark as paid (simulate successful payment)
          if (finalizedInvoice.id) {
            await stripe.invoices.pay(finalizedInvoice.id, {
              source: 'tok_visa', // Test token
            });
          }

          createdInvoices.push(finalizedInvoice);
        }
      }
    }

    return {
      success: true,
      message: `Successfully created test data for integration ${orgIntegrationId}`,
      summary: {
        integration: integration.id,
        environment: integration.environment,
        livemode: integration.settings?.livemode,
        productsCreated: createdProducts.length,
        customersCreated: createdCustomers.length,
        invoicesCreated: createdInvoices.length,
        periodMonths,
        yearlyChurnRate: `${(yearlyChurnRate * 100).toFixed(1)}%`,
        totalRevenue: createdInvoices.reduce(
          (sum, inv) => sum + (inv.amount_paid || 0),
          0
        ),
      },
    };
  }
);

interface AddSingleProductParams {
  orgIntegrationId: string;
}

export const addSingleProduct = inngest.createFunction(
  { id: 'add-single-product' },
  { event: 'stripe/add.single.product' },
  async ({ event }) => {
    const { orgIntegrationId } = event.data as AddSingleProductParams;

    // Get and validate integration
    const integration = await getIntegrationById(orgIntegrationId);
    if (!integration) {
      throw new Error(`Integration with ID ${orgIntegrationId} not found`);
    }

    // SAFETY CHECK: Ensure this is NOT a live integration
    if (integration.settings?.livemode === true) {
      throw new Error(
        'SAFETY ERROR: Cannot add test product to a live Stripe integration!'
      );
    }

    if (integration.environment === 'production') {
      throw new Error(
        'SAFETY ERROR: Cannot add test product to a production environment integration!'
      );
    }

    // Initialize Stripe client
    const accessToken = getAccessTokenFromIntegration(integration);
    if (!accessToken) {
      throw new Error('No access token found for integration');
    }

    const stripe = new Stripe(accessToken, {
      apiVersion: '2025-08-27.basil',
    });

    // Fetch Stripe account identity
    const account = await stripe.accounts.retrieve();
    console.log('Stripe Account Identity:', {
      id: account.id,
      email: account.email,
      country: account.country,
      default_currency: account.default_currency,
      business_profile: account.business_profile,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
    });

    // Create a single test product
    const testProduct = await stripe.products.create({
      name: 'Test Product - Single Add',
      type: 'service',
      description: 'A test product created by the addSingleProduct function',
      metadata: {
        type: 'test',
        created_by: 'addSingleProduct_function',
        timestamp: new Date().toISOString(),
      },
    });

    console.log('Created Test Product:', testProduct);

    // Create a price for the product
    const testPrice = await stripe.prices.create({
      product: testProduct.id,
      unit_amount: 2500, // $25.00
      currency: 'usd',
      metadata: {
        created_by: 'addSingleProduct_function',
      },
    });

    console.log('Created Test Price:', testPrice);

    // Create a test customer for the invoices
    const testCustomer = await stripe.customers.create({
      name: 'Test Customer for Single Product',
      email: '<EMAIL>',
      description: 'Test customer created for single product invoices',
      metadata: {
        created_by: 'addSingleProduct_function',
        timestamp: new Date().toISOString(),
      },
    });

    console.log('Created Test Customer:', testCustomer);

    // Create 10 invoices dated back to 2 months ago
    const createdInvoices = [];
    const numberOfInvoices = 10;
    const twoMonthsAgo = new Date();
    twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);

    for (let i = 0; i < numberOfInvoices; i++) {
      // Spread invoices over the 2-month period
      const invoiceDate = new Date(twoMonthsAgo);
      invoiceDate.setDate(invoiceDate.getDate() + i * 6); // Space them out every 6 days

      // Create invoice item first
      await stripe.invoiceItems.create({
        customer: testCustomer.id,
        amount: testPrice.unit_amount || 2500, // Fallback to $25.00 if null
        currency: 'usd',
        description: testProduct.name,
        metadata: {
          generated: 'true',
          created_by: 'addSingleProduct_function',
          invoice_number: (i + 1).toString(),
        },
      });

      // Create and finalize invoice
      const invoice = await stripe.invoices.create({
        customer: testCustomer.id,
        collection_method: 'charge_automatically',
        auto_advance: false, // We'll finalize manually
        metadata: {
          generated: 'true',
          created_by: 'addSingleProduct_function',
          invoice_number: (i + 1).toString(),
        },
      });

      // Finalize the invoice
      if (invoice.id) {
        const finalizedInvoice = await stripe.invoices.finalizeInvoice(
          invoice.id
        );

        // Mark as paid (simulate successful payment)
        if (finalizedInvoice.id) {
          await stripe.invoices.pay(finalizedInvoice.id, {
            source: 'tok_visa', // Test token for successful payment
          });
        }

        createdInvoices.push(finalizedInvoice);
        console.log(
          `Created and paid invoice ${i + 1}/10:`,
          finalizedInvoice.id
        );
      }
    }

    // Get list of all products for this organization
    const productsList = await stripe.products.list({
      limit: 100, // Get up to 100 products
    });

    console.log('All Products for Organization:', {
      total_count: productsList.data.length,
      has_more: productsList.has_more,
      products: productsList.data.map(p => ({
        id: p.id,
        name: p.name,
        type: p.type,
        created: new Date(p.created * 1000).toISOString(),
        metadata: p.metadata,
      })),
    });

    return {
      success: true,
      message: `Successfully added test product, created customer, and generated ${numberOfInvoices} paid invoices for integration ${orgIntegrationId}`,
      account_info: {
        id: account.id,
        email: account.email,
        country: account.country,
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
      },
      created_product: {
        id: testProduct.id,
        name: testProduct.name,
        price_id: testPrice.id,
        amount: testPrice.unit_amount,
      },
      created_customer: {
        id: testCustomer.id,
        name: testCustomer.name,
        email: testCustomer.email,
      },
      created_invoices: {
        count: createdInvoices.length,
        total_amount: createdInvoices.reduce(
          (sum, inv) => sum + (inv.amount_paid || 0),
          0
        ),
        invoice_ids: createdInvoices.map(inv => inv.id),
      },
      products_summary: {
        total_products: productsList.data.length,
        has_more: productsList.has_more,
        product_names: productsList.data.map(p => p.name),
      },
    };
  }
);
