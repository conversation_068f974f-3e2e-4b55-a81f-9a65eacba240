'use client';

import { useState } from 'react';

export default function ConnectStripeButton() {
  const [loading, setLoading] = useState(false);

  const handleStripeConnect = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/integrations/stripe/connect', {
        credentials: 'include',
      });

      if (!response.ok) {
        console.error('Failed to get Stripe OAuth URL:', await response.text());
        return;
      }

      const data = await response.json();
      window.location.href = data.url;
    } catch (err) {
      console.error('Error connecting to Stripe:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleStripeConnect}
      disabled={loading}
      className='w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-md transition-colors duration-200'
    >
      {loading ? 'Connecting…' : 'Connect with <PERSON><PERSON>'}
    </button>
  );
}
